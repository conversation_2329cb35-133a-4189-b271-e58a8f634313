# 房联前端项目 AI 提示词使用规范文档

## 项目概述

这是一个基于 Vue 3 + Vite + Vant UI 的房联前端项目，采用现代化的前端开发技术栈。

## 技术栈

### 核心框架
- **Vue 3.5.17** - 使用 Composition API
- **Vite 7.0.3** - 构建工具
- **Vue Router 4.5.1** - 路由管理（Hash 模式）
- **Pinia 3.0.3** - 状态管理

### UI 组件库
- **Vant 4.9.20** - 移动端 UI 组件库
- **注意**: 项目已从 Element Plus 迁移到 Vant UI

### 工具库
- **@funi-lib/utils 0.1.4** - 房联内部工具库
- **pinia-plugin-persistedstate** - Pinia 持久化插件
- **unplugin-vue-router** - 自动路由生成

## 项目结构

```
src/
├── api/                    # API 接口定义
├── assets/                 # 静态资源
├── components/             # 公共组件
├── pages/                  # 页面组件（自动路由）
├── router/                 # 路由配置
├── stores/                 # Pinia 状态管理
│   └── modules/           # 状态模块
├── utils/                  # 工具函数
│   └── http/              # HTTP 请求封装
├── App.vue                # 根组件
├── main.js                # 入口文件
└── style.css              # 全局样式
```

## AI 提示词规范

### 1. 组件开发规范

#### 创建新页面组件
```
请在 src/pages/ 目录下创建一个新的页面组件，使用以下规范：
- 文件名使用 kebab-case 命名
- 使用 Vue 3 Composition API
- 使用 Vant UI 组件
- 包含 <template>、<script setup>、<style scoped> 三个部分
```

#### 创建公共组件
```
请在 src/components/ 目录下创建一个公共组件，遵循：
- 组件名使用 PascalCase
- 使用 defineProps 和 defineEmits
- 添加适当的 TypeScript 类型注解（如果需要）
```

### 2. 路由相关

#### 自动路由系统
```
项目使用 unplugin-vue-router 自动生成路由：
- 在 src/pages/ 目录下的 .vue 文件会自动生成路由
- 文件路径即为路由路径
- 支持动态路由和嵌套路由
```

### 3. 状态管理

#### 创建新的 Store
```
请在 src/stores/modules/ 目录下创建新的 Pinia store：
- 使用 defineStore 定义
- 支持持久化存储（persist: true）
- 在 src/stores/index.js 中导出
```

### 4. HTTP 请求

#### 使用全局 HTTP 实例
```
项目已将 HTTP 实例挂载到全局：
- 使用 window.$http 进行 API 调用
- 支持加密传输和自动解密
- 内置错误处理和用户认证
```

### 5. UI 组件使用

#### Vant UI 组件
```
项目使用 Vant UI 库，请遵循：
- 使用 van- 前缀的组件
- 参考 Vant 官方文档的 API
- 移动端优先设计
- 支持主题配置（通过 van-config-provider）
```

### 6. 样式规范

#### CSS 预处理器
```
项目支持 SCSS：
- 在 <style> 标签中使用 lang="scss"
- 使用 scoped 避免样式污染
- 可以使用全局样式变量
```

### 7. 别名配置

#### 路径别名
```
项目配置了以下别名：
- @ -> src/
- ~ -> src/assets/
- ~root -> 项目根目录
```

## 常用提示词模板

### 创建页面
```
请创建一个 [页面名称] 页面，位于 src/pages/[文件名].vue，包含：
- 使用 Vant UI 组件
- 响应式设计
- 适当的状态管理
- 错误处理
```

### 创建组件
```
请创建一个 [组件名称] 组件，位于 src/components/[ComponentName].vue，要求：
- 可复用性
- Props 类型定义
- 事件发射
- 样式封装
```

### API 集成
```
请在 src/api/ 目录下创建 [模块名称] 的 API 接口，使用：
- window.$http 进行请求
- 适当的错误处理
- TypeScript 类型定义（如果需要）
```

### 状态管理
```
请创建一个 [模块名称] 的 Pinia store，包含：
- 状态定义
- 计算属性
- 异步操作
- 持久化配置
```

## 注意事项

1. **UI 库迁移**: 项目已从 Element Plus 迁移到 Vant UI，请确保使用 Vant 组件
2. **移动端优先**: 项目面向移动端，请考虑触摸交互和响应式设计
3. **房联规范**: 遵循房联内部的开发规范和代码风格
4. **安全性**: 注意数据加密和用户认证相关的安全要求
5. **性能优化**: 使用 keep-alive 缓存页面，合理使用懒加载

## 开发命令

```bash
# 开发环境
npm run dev

# 构建生产环境
npm run build

# 预览构建结果
npm run preview
```

## 服务器配置

- 开发服务器端口: 8000
- 代理配置: /api -> 后端服务
- 支持热更新和自动刷新

## 代码示例

### 1. 标准页面组件模板

```vue
<template>
  <div class="page-container">
    <van-nav-bar title="页面标题" left-arrow @click-left="$router.back()" />

    <div class="content">
      <van-cell-group>
        <van-field v-model="form.name" label="姓名" placeholder="请输入姓名" />
      </van-cell-group>

      <van-button type="primary" block @click="handleSubmit">
        提交
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showNotify } from 'vant'

// 响应式数据
const form = reactive({
  name: ''
})

// 提交处理
const handleSubmit = async () => {
  try {
    const result = await window.$http.post('/api/submit', form)
    showNotify({ type: 'success', message: '提交成功' })
  } catch (error) {
    showNotify({ type: 'danger', message: '提交失败' })
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding: 16px;
}
</style>
```

### 2. Pinia Store 模板

```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref({})
  const token = ref('')

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)

  // 方法
  const login = async (credentials) => {
    try {
      const response = await window.$http.post('/api/login', credentials)
      token.value = response.token
      userInfo.value = response.user
      return response
    } catch (error) {
      throw error
    }
  }

  const logout = () => {
    token.value = ''
    userInfo.value = {}
    sessionStorage.clear()
  }

  return {
    userInfo,
    token,
    isLoggedIn,
    login,
    logout
  }
}, {
  persist: true
})

export default useUserStore
```

### 3. API 模块模板

```javascript
// src/api/user.js
class UserApi {
  // 获取用户信息
  static async getUserInfo(userId) {
    return window.$http.fetch(`/api/user/${userId}`)
  }

  // 更新用户信息
  static async updateUser(userId, data) {
    return window.$http.post(`/api/user/${userId}`, data)
  }

  // 上传头像
  static async uploadAvatar(file) {
    const formData = new FormData()
    formData.append('avatar', file)
    return window.$http.upload2('/api/user/avatar', formData)
  }
}

export default UserApi
```

## 最佳实践

### 1. 错误处理
```javascript
// 统一错误处理
const handleApiCall = async (apiCall) => {
  try {
    const result = await apiCall()
    return { success: true, data: result }
  } catch (error) {
    console.error('API调用失败:', error)
    return { success: false, error }
  }
}
```

### 2. 表单验证
```javascript
import { ref } from 'vue'

const useFormValidation = () => {
  const errors = ref({})

  const validateRequired = (value, fieldName) => {
    if (!value || value.trim() === '') {
      errors.value[fieldName] = `${fieldName}不能为空`
      return false
    }
    delete errors.value[fieldName]
    return true
  }

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      errors.value.email = '邮箱格式不正确'
      return false
    }
    delete errors.value.email
    return true
  }

  return {
    errors,
    validateRequired,
    validateEmail
  }
}
```

### 3. 组件通信
```javascript
// 父组件
<template>
  <ChildComponent
    :data="parentData"
    @update="handleChildUpdate"
  />
</template>

// 子组件
<script setup>
const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

const handleClick = () => {
  emit('update', newData)
}
</script>
```

## 调试技巧

### 1. 开发工具
- 使用 Vue DevTools 调试组件状态
- 使用 Pinia DevTools 查看状态变化
- 使用浏览器网络面板监控 API 请求

### 2. 日志输出
```javascript
// 开发环境日志
if (import.meta.env.DEV) {
  console.log('调试信息:', data)
}
```

### 3. 性能监控
```javascript
// 性能标记
performance.mark('api-start')
await apiCall()
performance.mark('api-end')
performance.measure('api-duration', 'api-start', 'api-end')
```
