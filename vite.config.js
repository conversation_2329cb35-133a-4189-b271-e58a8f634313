import path from 'node:path'
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import VueRouter from "unplugin-vue-router/vite";

// https://vite.dev/config/
export default defineConfig({
  base: "./",
  plugins: [
    vue(),
    VueRouter({
      extensions: [".vue"],
      routesFolder: "src/pages",
      exclude: ['src/pages/**/components/**',,'src/pages/**/common/**'],//排除components和common文件夹
    }),
  ],
  server: {
    host: true,
    port: 8000,
    proxy: {
      "/api": {
        target: "",
        ws: false,
        changeOrigin: true,
      },
    },
  },
  resolve: {
    alias: {
      "@": path.join(__dirname, "./src"),
      "~": path.join(__dirname, "./src/assets"),
      "~root": path.join(__dirname, "."),
    },
  },
});
